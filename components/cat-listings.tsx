"use client";

import { <PERSON> } from "@/lib/i18n/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { CatCard } from "@/components/cat-card";
import { Pagination } from "@/components/pagination";
import { useTranslations } from "next-intl";
import { useFavorites } from "@/hooks/use-favorites";
import { catListingCache } from "@/lib/search-params";

export function CatListings() {
	// Get parsed search parameters from nuqs server cache
	const gender = catListingCache.get("gender");
	const specialNeeds = catListingCache.get("specialNeeds");
	const vaccinated = catListingCache.get("vaccinated");
	const neutered = catListingCache.get("neutered");
	const available = catListingCache.get("available");
	const search = catListingCache.get("search");
	const page = catListingCache.get("page");
	const sort = catListingCache.get("sort");
	const ageMin = catListingCache.get("ageMin");
	const ageMax = catListingCache.get("ageMax");
	const breedId = catListingCache.get("breedId");
	const wilayaId = catListingCache.get("wilayaId");
	const communeId = catListingCache.get("communeId");

	const utils = api.useUtils();
	const t = useTranslations("cats");

	// Fetch cats using tRPC
	const { data, isLoading, error } = api.cats.getAll.useQuery(
		{
			gender:
				gender === "all"
					? undefined
					: (gender as "male" | "female" | undefined),
			specialNeeds: specialNeeds || undefined,
			vaccinated: vaccinated || undefined,
			neutered: neutered || undefined,
			notAdopted: available || undefined,
			search,
			page,
			sort: sort as
				| "newest"
				| "oldest"
				| "name_asc"
				| "name_desc"
				| undefined,
			ageMin,
			ageMax,
			breedId,
			wilayaId,
			communeId,
			limit: 12,
		},
		{
			staleTime: 30000, // 30 seconds
			refetchOnWindowFocus: false, // Prevent unnecessary refetches
		}
	);

	// Use favorites hook with additional queries to invalidate
	const { favoriteMap, handleToggleFavorite, pendingFavoriteId } =
		useFavorites({
			additionalQueriesToInvalidate: [
				() => utils.cats.getAll.invalidate(),
			],
		});

	// Show loading state
	if (isLoading) {
		return (
			<div className="w-full flex flex-col items-center justify-center py-12">
				<Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
				<p className="text-muted-foreground">{t("loading")}</p>
			</div>
		);
	}

	// Show error state
	if (error) {
		return (
			<div className="text-center py-12 w-full">
				<h3 className="text-xl font-medium mb-2 text-destructive">
					{t("errors.loadingError")}
				</h3>
				<p className="text-muted-foreground mb-6">
					{error.message || t("errors.genericError")}
				</p>
				<Button asChild>
					<Link href="/cats">{t("retry")}</Link>
				</Button>
			</div>
		);
	}

	// Show empty state if no cats found
	if (!data || data.cats.length === 0) {
		return (
			<div className="w-full">
				{/* Search result feedback for empty results */}
				{search && search.trim() && (
					<div className="mb-4 p-4 bg-muted/50 rounded-lg">
						<p className="text-sm font-medium">
							{t("search.noResultsFor", { query: search.trim() })}
						</p>
						<div className="mt-2 text-sm text-muted-foreground">
							<p>{t("search.suggestions.tryDifferent")}</p>
							<ul className="mt-1 list-disc list-inside">
								<li>{t("search.suggestions.checkSpelling")}</li>
								<li>{t("search.suggestions.useGeneral")}</li>
								<li>{t("search.suggestions.tryBreed")}</li>
								<li>{t("search.suggestions.tryLocation")}</li>
							</ul>
						</div>
					</div>
				)}

				<div className="text-center py-12">
					<h3 className="text-xl font-medium mb-2">
						{t("noCatsFound")}
					</h3>
					<p className="text-muted-foreground mb-6">
						{search
							? t("search.tryDifferentTerms")
							: t("adjustFilters")}
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="w-full">
			{/* Search result feedback for successful results */}
			{search && search.trim() && (
				<div className="mb-4 p-4 bg-muted/50 rounded-lg">
					<p className="text-sm font-medium">
						{t("search.resultsCount", {
							count: data.pagination.total,
							query: search.trim(),
						})}
					</p>
				</div>
			)}

			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
				{data.cats.map((cat) => (
					<CatCard
						key={cat.id}
						cat={{
							...cat,
							isFavorite: favoriteMap.has(cat.id),
						}}
						actions={{
							mode: "view",
							onToggleFavorite: handleToggleFavorite,
							togglePending: pendingFavoriteId === cat.id,
						}}
					/>
				))}
			</div>

			<Pagination
				currentPage={page}
				totalPages={data.pagination.pageCount}
			/>
		</div>
	);
}
