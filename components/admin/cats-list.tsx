"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { MoreHorizontal, Search, Check, X, Edit, Trash } from "lucide-react"

// Mock data for cats
const mockCats = [
  {
    id: "1",
    name: "Whiskers",
    age: "2 years",
    gender: "Male",
    breed: "Domestic Shorthair",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Whiskers",
    location: "New York, NY",
    postedBy: "Happy Paws Rescue",
    postedDate: "2023-05-15",
    status: "approved",
  },
  {
    id: "2",
    name: "Luna",
    age: "1 year",
    gender: "Female",
    breed: "Siamese Mix",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Luna",
    location: "Boston, MA",
    postedBy: "Feline Friends",
    postedDate: "2023-05-10",
    status: "pending",
  },
  {
    id: "3",
    name: "Oliver",
    age: "3 years",
    gender: "Male",
    breed: "Tabby",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Oliver",
    location: "Chicago, IL",
    postedBy: "Second Chance Shelter",
    postedDate: "2023-05-05",
    status: "approved",
  },
  {
    id: "4",
    name: "Bella",
    age: "4 years",
    gender: "Female",
    breed: "Persian",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Bella",
    location: "Los Angeles, CA",
    postedBy: "Whisker Haven",
    postedDate: "2023-04-30",
    status: "approved",
  },
  {
    id: "5",
    name: "Max",
    age: "2 years",
    gender: "Male",
    breed: "Maine Coon Mix",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Max",
    location: "Seattle, WA",
    postedBy: "Purrfect Match Rescue",
    postedDate: "2023-04-25",
    status: "rejected",
  },
]

export function AdminCatsList({ limit }: { limit?: number }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [catToDelete, setCatToDelete] = useState<string | null>(null)
  const { toast } = useToast()

  const filteredCats = mockCats
    .filter(
      (cat) =>
        cat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cat.breed.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cat.postedBy.toLowerCase().includes(searchQuery.toLowerCase()),
    )
    .slice(0, limit)

  const handleStatusChange = (catId: string, newStatus: string) => {
    toast({
      title: "Status updated",
      description: `Cat #${catId} status changed to ${newStatus}`,
    })
  }

  const handleDeleteClick = (catId: string) => {
    setCatToDelete(catId)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (catToDelete) {
      toast({
        title: "Cat deleted",
        description: `Cat #${catToDelete} has been deleted`,
      })
      setCatToDelete(null)
      setDeleteDialogOpen(false)
    }
  }

  return (
    <div className="space-y-4">
      {!limit && (
        <div className="flex items-center">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search cats..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      )}

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Cat</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Posted By</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCats.map((cat) => (
              <TableRow key={cat.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="relative h-10 w-10 rounded-md overflow-hidden bg-muted">
                      <Image
                        src={cat.imageUrl || "https://placehold.co/100x100/e2e8f0/94a3b8?text=Cat"}
                        alt={cat.name}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "https://placehold.co/100x100/e2e8f0/94a3b8?text=Cat"
                        }}
                      />
                    </div>
                    <div>
                      <div className="font-medium">{cat.name}</div>
                      <div className="text-sm text-muted-foreground">ID: {cat.id}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{cat.breed}</div>
                    <div>
                      {cat.age} • {cat.gender}
                    </div>
                    <div className="text-muted-foreground">{cat.location}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{cat.postedBy}</div>
                    <div className="text-muted-foreground">{new Date(cat.postedDate).toLocaleDateString()}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <StatusBadge status={cat.status} />
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/cats/${cat.id}`}>View Details</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/cats/${cat.id}/edit`}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleStatusChange(cat.id, "approved")}>
                        <Check className="h-4 w-4 mr-2 text-green-500" />
                        Approve
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(cat.id, "rejected")}>
                        <X className="h-4 w-4 mr-2 text-red-500" />
                        Reject
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleDeleteClick(cat.id)} className="text-red-500">
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}

            {filteredCats.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                  No cats found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this cat listing? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "approved":
      return (
        <Badge variant="outline" className="border-green-500 text-green-500">
          Approved
        </Badge>
      )
    case "pending":
      return (
        <Badge variant="outline" className="border-amber-500 text-amber-500">
          Pending
        </Badge>
      )
    case "rejected":
      return (
        <Badge variant="outline" className="border-red-500 text-red-500">
          Rejected
        </Badge>
      )
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
