"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AdminCatsList } from "@/components/admin/cats-list"
import { AdminUsersList } from "@/components/admin/users-list"
import { AdminClinicsList } from "@/components/admin/clinics-list"
import { AdminStats } from "@/components/admin/stats"

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="mb-6">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="cats">Cats</TabsTrigger>
        <TabsTrigger value="users">Users</TabsTrigger>
        <TabsTrigger value="clinics">Clinics</TabsTrigger>
      </TabsList>

      <TabsContent value="overview">
        <div className="space-y-6">
          <AdminStats />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Cat Listings</CardTitle>
                <CardDescription>The most recently added cats</CardDescription>
              </CardHeader>
              <CardContent>
                <AdminCatsList limit={5} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent User Registrations</CardTitle>
                <CardDescription>The most recently registered users</CardDescription>
              </CardHeader>
              <CardContent>
                <AdminUsersList limit={5} />
              </CardContent>
            </Card>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="cats">
        <Card>
          <CardHeader>
            <CardTitle>Manage Cat Listings</CardTitle>
            <CardDescription>View, approve, edit, or remove cat listings</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminCatsList />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="users">
        <Card>
          <CardHeader>
            <CardTitle>Manage Users</CardTitle>
            <CardDescription>View and manage user accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminUsersList />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="clinics">
        <Card>
          <CardHeader>
            <CardTitle>Manage Clinics</CardTitle>
            <CardDescription>View and manage veterinary clinic profiles</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminClinicsList />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
