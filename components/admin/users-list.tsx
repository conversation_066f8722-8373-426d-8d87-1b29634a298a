"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { MoreHorizontal, Search, Edit, Trash, Ban, Shield } from "lucide-react"

// Mock data for users
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "adopter",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=John",
    location: "New York, NY",
    joinedDate: "2023-01-15",
    status: "active",
  },
  {
    id: "2",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    role: "rescuer",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Jane",
    location: "Boston, MA",
    joinedDate: "2023-02-10",
    status: "active",
  },
  {
    id: "3",
    name: "Central Pet Clinic",
    email: "<EMAIL>",
    role: "clinic",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic",
    location: "Chicago, IL",
    joinedDate: "2023-03-05",
    status: "active",
  },
  {
    id: "4",
    name: "Happy Paws Rescue",
    email: "<EMAIL>",
    role: "rescuer",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Rescue",
    location: "Los Angeles, CA",
    joinedDate: "2023-04-20",
    status: "active",
  },
  {
    id: "5",
    name: "Michael Johnson",
    email: "<EMAIL>",
    role: "adopter",
    imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Michael",
    location: "Seattle, WA",
    joinedDate: "2023-05-25",
    status: "suspended",
  },
]

export function AdminUsersList({ limit }: { limit?: number }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const { toast } = useToast()

  const filteredUsers = mockUsers
    .filter(
      (user) =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.role.toLowerCase().includes(searchQuery.toLowerCase()),
    )
    .slice(0, limit)

  const handleStatusChange = (userId: string, newStatus: string) => {
    toast({
      title: "Status updated",
      description: `User #${userId} status changed to ${newStatus}`,
    })
  }

  const handleDeleteClick = (userId: string) => {
    setUserToDelete(userId)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (userToDelete) {
      toast({
        title: "User deleted",
        description: `User #${userToDelete} has been deleted`,
      })
      setUserToDelete(null)
      setDeleteDialogOpen(false)
    }
  }

  return (
    <div className="space-y-4">
      {!limit && (
        <div className="flex items-center">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      )}

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="relative h-10 w-10 rounded-full overflow-hidden bg-muted">
                      <Image
                        src={user.imageUrl || "https://placehold.co/100x100/e2e8f0/94a3b8?text=User"}
                        alt={user.name}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "https://placehold.co/100x100/e2e8f0/94a3b8?text=User"
                        }}
                      />
                    </div>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <RoleBadge role={user.role} />
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{user.location}</div>
                    <div className="text-muted-foreground">Joined {new Date(user.joinedDate).toLocaleDateString()}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <StatusBadge status={user.status} />
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/profile/${user.id}`}>View Profile</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/users/${user.id}/edit`}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {user.status === "active" ? (
                        <DropdownMenuItem onClick={() => handleStatusChange(user.id, "suspended")}>
                          <Ban className="h-4 w-4 mr-2 text-amber-500" />
                          Suspend
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={() => handleStatusChange(user.id, "active")}>
                          <Shield className="h-4 w-4 mr-2 text-green-500" />
                          Activate
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleDeleteClick(user.id)} className="text-red-500">
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}

            {filteredUsers.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                  No users found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function RoleBadge({ role }: { role: string }) {
  switch (role) {
    case "admin":
      return <Badge className="bg-purple-500">Admin</Badge>
    case "rescuer":
      return <Badge className="bg-blue-500">Rescuer</Badge>
    case "clinic":
      return <Badge className="bg-green-500">Clinic</Badge>
    case "adopter":
      return <Badge variant="outline">Adopter</Badge>
    default:
      return <Badge variant="outline">{role}</Badge>
  }
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "active":
      return (
        <Badge variant="outline" className="border-green-500 text-green-500">
          Active
        </Badge>
      )
    case "suspended":
      return (
        <Badge variant="outline" className="border-red-500 text-red-500">
          Suspended
        </Badge>
      )
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
