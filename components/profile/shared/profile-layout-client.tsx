"use client";

import type React from "react";
import { useState, ReactNode } from "react";
import { useLocale } from "next-intl";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { DashboardSidebar } from "./dashboard-sidebar";
import { DashboardHeader } from "./dashboard-header";
import { Star, Cat, Stethoscope, MessageCircle, Settings } from "lucide-react";
import type { User, SidebarItem, QuickStat } from "@/lib/types/profile";
import { api } from "@/lib/trpc/react";

interface ProfileLayoutClientProps {
	user: User;
	children: ReactNode;
}

export function ProfileLayoutClient({
	user,
	children,
}: ProfileLayoutClientProps) {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const locale = useLocale();
	const isRTL = locale === "ar";
	const pathname = usePathname();

	// Fetch user stats for sidebar
	const { data: userStats, isLoading: isLoadingStats } =
		api.users.getUserStats.useQuery();

	// Determine active route from pathname
	const getActiveRoute = () => {
		if (pathname.includes("/profile/cats")) return "cats";
		if (pathname.includes("/profile/favorites")) return "favorites";
		if (pathname.includes("/profile/settings")) return "settings";
		if (pathname.includes("/profile/services")) return "services";
		if (pathname.includes("/profile/messages")) return "messages";
		return "dashboard";
	};

	const activeRoute = getActiveRoute();

	// Get navigation items based on user role
	const getNavigationItems = (): SidebarItem[] => {
		const baseItems: SidebarItem[] = [
			{
				id: "favorites",
				labelKey: "favorites",
				icon: Star,
				descriptionKey: "favoritesDescription",
			},
		];

		// Add role-specific items
		if (user.role === "rescuer" || user.role === "clinic") {
			baseItems.unshift({
				id: "cats",
				labelKey: "listedCats",
				icon: Cat,
				descriptionKey: "listedCatsDescription",
			});
		}

		if (user.role === "clinic") {
			baseItems.push({
				id: "services",
				labelKey: "services",
				icon: Stethoscope,
				descriptionKey: "servicesDescription",
			});
		}

		// Add common items
		baseItems.push(
			{
				id: "messages",
				labelKey: "messages.title",
				icon: MessageCircle,
				descriptionKey: "messages.description",
			},
			{
				id: "settings",
				labelKey: "settings.title",
				icon: Settings,
				descriptionKey: "settings.description",
			}
		);

		return baseItems;
	};

	const navigationItems = getNavigationItems();

	// Get quick stats with dynamic data
	const quickStats: QuickStat[] = [
		{
			value: isLoadingStats ? 0 : (userStats?.listedCats ?? 0),
			labelKey: "listedCats",
			color: "teal",
		},
		{
			value: isLoadingStats ? 0 : (userStats?.favorites ?? 0),
			labelKey: "favorites",
			color: "purple",
		},
		{
			value: isLoadingStats ? 0 : (userStats?.messages ?? 0),
			labelKey: "messages.title",
			color: "blue",
		},
	];

	const colorClasses = {
		teal: "bg-teal-50 text-teal-600",
		purple: "bg-purple-50 text-purple-600",
		blue: "bg-blue-50 text-blue-600",
		green: "bg-green-50 text-green-600",
	};

	// Handle navigation - this will be handled by Link components
	const handleNavigation = (_route: string) => {
		// This will be handled by Link components in the sidebar
		setIsSidebarOpen(false);
	};

	return (
		<div className="bg-gray-50 flex flex-col h-full lg:flex-row">
			{/* Mobile Sidebar Overlay */}
			{isSidebarOpen && (
				<div className="lg:hidden fixed inset-0 z-50 flex">
					{/* Backdrop */}
					<div
						className="fixed inset-0 bg-gray-500 opacity-50"
						onClick={() => setIsSidebarOpen(false)}
					/>

					{/* Mobile Sidebar */}
					<div
						className={cn(
							"relative h-screen w-80 max-w-[80vw] bg-white flex flex-col",
							isRTL ? "ml-auto" : "mr-auto"
						)}
					>
						<DashboardSidebar
							user={user}
							navigationItems={navigationItems}
							quickStats={quickStats}
							activeTab={activeRoute}
							onTabChange={handleNavigation}
							isOpen={isSidebarOpen}
							onClose={() => setIsSidebarOpen(false)}
							isRTL={isRTL}
							colorClasses={colorClasses}
							className="flex flex-col h-full"
							useRouting={true}
							isLoadingStats={isLoadingStats}
						/>
					</div>
				</div>
			)}

			{/* Desktop Sidebar */}
			<DashboardSidebar
				user={user}
				navigationItems={navigationItems}
				quickStats={quickStats}
				activeTab={activeRoute}
				onTabChange={handleNavigation}
				isOpen={isSidebarOpen}
				onClose={() => setIsSidebarOpen(false)}
				isRTL={isRTL}
				colorClasses={colorClasses}
				className={cn(
					"hidden h-full lg:flex w-80 bg-white border-gray-200 flex-col sticky top-0",
					isRTL ? "border-l" : "border-r"
				)}
				useRouting={true}
				isLoadingStats={isLoadingStats}
			/>

			{/* Main Content */}
			<div className="flex-1 flex flex-col lg:min-h-0 min-w-0">
				{/* Header */}
				<DashboardHeader
					navigationItems={navigationItems}
					activeTab={activeRoute}
					onMenuClick={() => setIsSidebarOpen(true)}
					isRTL={isRTL}
					user={user}
				/>

				{/* Content */}
				<main className="flex-1 overflow-auto p-4 lg:p-6 min-w-0">
					{children}
				</main>
			</div>
		</div>
	);
}
