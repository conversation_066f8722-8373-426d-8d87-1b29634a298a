"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { userRoleEnum } from "@/lib/db/schema";
import { api } from "@/lib/trpc/react";
import { authClient } from "@/lib/auth/client";
import { AvatarUploader } from "@/components/ui/avatar-uploader";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

interface User {
	id: number;
	name: string;
	email: string;
	role: (typeof userRoleEnum.enumValues)[number];
	bio: string | null;
	location: string | null;
	wilayaId: number | null;
	communeId: number | null;
	phone: string | null;
	image: string | null;
	createdAt: string | Date;
	updatedAt: string | Date;
	emailVerified: boolean;
	wilaya?: {
		id: number;
		name: string;
		code: string;
	} | null;
	commune?: {
		id: number;
		name: string;
	} | null;
}

const profileSchema = z.object({
	name: z.string().min(2, {
		message: "Name must be at least 2 characters.",
	}),
	email: z.string().email({
		message: "Please enter a valid email address.",
	}),
	bio: z.string().optional(),
	location: z.string().optional(),
	phone: z.string().optional(),
	image: z.string().nullable().optional(),
});

const passwordSchema = z
	.object({
		currentPassword: z.string().min(1, {
			message: "Please enter your current password.",
		}),
		newPassword: z.string().min(8, {
			message: "Password must be at least 8 characters.",
		}),
		confirmPassword: z.string().min(8, {
			message: "Password must be at least 8 characters.",
		}),
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

type ProfileValues = z.infer<typeof profileSchema>;
type PasswordValues = z.infer<typeof passwordSchema>;

export function ProfileSettings({ user }: { user: User }) {
	const router = useRouter();
	const t = useTranslations("profile.settings");
	const commonT = useTranslations("common");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [avatarImage, setAvatarImage] = useState<string | null>(user.image);
	const { toast } = useToast();
	const utils = api.useUtils();
	const updateProfile = trpc.users.updateProfile.useMutation({
		onSuccess: () => {
			// Invalidate the user profile query to refetch the updated data
			utils.users.getProfile.invalidate();

			router.refresh();

			toast({
				title: t("profileUpdated"),
				description: t("profileUpdatedDescription"),
			});

			setIsSubmitting(false);
		},
		onError: (error) => {
			toast({
				title: commonT("error"),
				description: error.message || t("profileUpdateError"),
				variant: "destructive",
			});

			setIsSubmitting(false);
		},
	});

	const profileForm = useForm<ProfileValues>({
		resolver: zodResolver(profileSchema),
		defaultValues: {
			name: user.name,
			email: user.email,
			bio: user.bio || "",
			location: user.location || "",
			phone: user.phone || "",
			image: user.image,
		},
	});

	const passwordForm = useForm<PasswordValues>({
		resolver: zodResolver(passwordSchema),
		defaultValues: {
			currentPassword: "",
			newPassword: "",
			confirmPassword: "",
		},
	});

	const handleAvatarChange = (imageData: string | null) => {
		setAvatarImage(imageData);
		profileForm.setValue("image", imageData);
	};

	async function onProfileSubmit(values: ProfileValues) {
		setIsSubmitting(true);

		// Use tRPC mutation to update the profile
		updateProfile.mutate({
			name: values.name,
			bio: values.bio,
			location: values.location,
			phone: values.phone,
			image: values.image || undefined,
		});
	}

	async function onPasswordSubmit(values: PasswordValues) {
		setIsSubmitting(true);

		try {
			await authClient.changePassword({
				newPassword: values.newPassword,
				currentPassword: values.currentPassword,
				revokeOtherSessions: true, // revoke all other sessions the user is signed into
			});

			toast({
				title: t("passwordUpdated"),
				description: t("passwordUpdatedDescription"),
			});
			passwordForm.reset({
				currentPassword: "",
				newPassword: "",
				confirmPassword: "",
			});
		} catch (error) {
			toast({
				title: commonT("error"),
				description: t("passwordUpdateError"),
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	}

	return (
		<Tabs defaultValue="profile" className="space-y-6">
			<TabsList>
				<TabsTrigger value="profile">{t("profileInfo")}</TabsTrigger>
				<TabsTrigger value="password">{t("password")}</TabsTrigger>
				{/* <TabsTrigger value="notifications">Notifications</TabsTrigger>
					<TabsTrigger value="privacy">Privacy</TabsTrigger> */}
			</TabsList>

			<TabsContent value="profile">
				<Card>
					<CardHeader>
						<CardTitle>{t("profileInfo")}</CardTitle>
						<CardDescription>
							{t("updatePersonalInfo")}
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Form {...profileForm}>
							<form
								onSubmit={profileForm.handleSubmit(
									onProfileSubmit
								)}
								className="space-y-6"
							>
								<div className="space-y-6">
									<div className="flex flex-col md:flex-row md:items-start gap-8">
										<AvatarUploader
											currentImage={avatarImage}
											onImageChange={handleAvatarChange}
										/>
										<div className="flex-1 space-y-1 text-center sm:text-left">
											<h3 className="text-lg font-medium">
												{t("profilePicture")}
											</h3>
											<p className="text-sm text-muted-foreground">
												{t("profilePictureDescription")}
											</p>
										</div>
									</div>

									<Separator className="my-6" />

									<FormField
										control={profileForm.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("name")}
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder={t(
															"namePlaceholder"
														)}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={profileForm.control}
										name="email"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("email")}
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														type="email"
														disabled
													/>
												</FormControl>
												<FormDescription>
													{t("emailDescription")}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={profileForm.control}
										name="bio"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("bio")}
												</FormLabel>
												<FormControl>
													<Textarea
														{...field}
														placeholder={t(
															"bioPlaceholder"
														)}
														rows={4}
													/>
												</FormControl>
												<FormDescription>
													{t("bioDescription")}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<div className="grid gap-4 sm:grid-cols-2">
										<FormField
											control={profileForm.control}
											name="location"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														{t("location")}
													</FormLabel>
													<FormControl>
														<Input
															{...field}
															placeholder={t(
																"locationPlaceholder"
															)}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={profileForm.control}
											name="phone"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														{t("phone")}
													</FormLabel>
													<FormControl>
														<Input
															{...field}
															placeholder={t(
																"phonePlaceholder"
															)}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>

								<Button type="submit" disabled={isSubmitting}>
									{isSubmitting
										? t("saving")
										: t("saveChanges")}
								</Button>
							</form>
						</Form>
					</CardContent>
				</Card>
			</TabsContent>

			<TabsContent value="password">
				<Card>
					<CardHeader>
						<CardTitle>{t("changePassword")}</CardTitle>
						<CardDescription>
							{t("changePasswordDescription")}
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Form {...passwordForm}>
							<form
								onSubmit={passwordForm.handleSubmit(
									onPasswordSubmit
								)}
								className="space-y-6"
							>
								<div className="space-y-4">
									<FormField
										control={passwordForm.control}
										name="currentPassword"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("currentPassword")}
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														type="password"
														placeholder="••••••••"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={passwordForm.control}
										name="newPassword"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("newPassword")}
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														type="password"
														placeholder="••••••••"
													/>
												</FormControl>
												<FormDescription>
													{t("passwordRequirement")}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={passwordForm.control}
										name="confirmPassword"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("confirmPassword")}
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														type="password"
														placeholder="••••••••"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<Button type="submit" disabled={isSubmitting}>
									{isSubmitting
										? t("updating")
										: t("updatePassword")}
								</Button>
							</form>
						</Form>
					</CardContent>
				</Card>
			</TabsContent>

			{/* <TabsContent value="notifications">
				<Card>
					<CardHeader>
						<CardTitle>Notification Settings</CardTitle>
						<CardDescription>
							Manage how you receive notifications
						</CardDescription>
					</CardHeader>
					<CardContent>
						<p className="text-muted-foreground">
							Notification settings will be available soon.
						</p>
					</CardContent>
				</Card>
			</TabsContent>

			<TabsContent value="privacy">
				<Card>
					<CardHeader>
						<CardTitle>Privacy Settings</CardTitle>
						<CardDescription>
							Manage your privacy preferences
						</CardDescription>
					</CardHeader>
					<CardContent>
						<p className="text-muted-foreground">
							Privacy settings will be available soon.
						</p>
					</CardContent>
				</Card>
			</TabsContent> */}
		</Tabs>
	);
}
