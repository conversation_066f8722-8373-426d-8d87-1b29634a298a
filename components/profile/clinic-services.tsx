"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Edit, Trash2, Plus, CheckCircle, Clock } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"

export function ClinicServices() {
  const { toast } = useToast()
  const [services, setServices] = useState([
    {
      id: "1",
      name: "Vaccinations",
      description: "Core and non-core vaccinations for cats of all ages. Includes FVRCP, rabies, and FeLV vaccines.",
      price: "$30-$60",
      isAvailable: true,
      requiresAppointment: false,
    },
    {
      id: "2",
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      description:
        "Surgical sterilization for cats. Includes pre-surgical examination, anesthesia, and post-operative care.",
      price: "$100-$200",
      isAvailable: true,
      requiresAppointment: true,
    },
    {
      id: "3",
      name: "Microchipping",
      description: "Permanent identification for your cat. Includes microchip implantation and registration.",
      price: "$45",
      isAvailable: true,
      requiresAppointment: false,
    },
    {
      id: "4",
      name: "Dental Cleaning",
      description:
        "Professional dental cleaning for cats. Includes scaling, polishing, and examination under anesthesia.",
      price: "$200-$400",
      isAvailable: false,
      requiresAppointment: true,
    },
  ])
  const [dialogOpen, setDialogOpen] = useState(false)
  const [serviceToDelete, setServiceToDelete] = useState<any>(null)

  const deleteService = (id: string) => {
    setServices(services.filter((service) => service.id !== id))
    setDialogOpen(false)
    toast({
      title: "Service deleted",
      description: "The service has been removed from your profile.",
    })
  }

  const openDeleteDialog = (service: any) => {
    setServiceToDelete(service)
    setDialogOpen(true)
  }

  const toggleServiceAvailability = (id: string) => {
    setServices(
      services.map((service) => (service.id === id ? { ...service, isAvailable: !service.isAvailable } : service)),
    )

    const service = services.find((s) => s.id === id)
    toast({
      title: service?.isAvailable ? "Service marked as unavailable" : "Service marked as available",
      description: `${service?.name} has been updated.`,
    })
  }

  if (services.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium mb-2">No services listed yet</h3>
        <p className="text-muted-foreground mb-6">
          Add services that your clinic offers to help cat owners and rescuers.
        </p>
        <Button asChild>
          <Link href="/profile/services/new">
            <Plus className="h-4 w-4 mr-2" />
            Add Service
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <p className="text-muted-foreground">Showing {services.length} services</p>
        <Button asChild>
          <Link href="/profile/services/new">
            <Plus className="h-4 w-4 mr-2" />
            Add Service
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {services.map((service) => (
          <Card key={service.id} className={`${!service.isAvailable ? "opacity-75" : ""}`}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-bold">{service.name}</h3>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={service.isAvailable}
                    onCheckedChange={() => toggleServiceAvailability(service.id)}
                    aria-label="Toggle availability"
                  />
                  <span className="text-sm text-muted-foreground">
                    {service.isAvailable ? "Available" : "Unavailable"}
                  </span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-3">
                {service.requiresAppointment ? (
                  <Badge variant="outline" className="border-amber-500 text-amber-500">
                    <Clock className="h-3.5 w-3.5 mr-1" />
                    Appointment Required
                  </Badge>
                ) : (
                  <Badge variant="outline" className="border-green-500 text-green-500">
                    <CheckCircle className="h-3.5 w-3.5 mr-1" />
                    Walk-in Available
                  </Badge>
                )}
                {service.price && <Badge variant="outline">{service.price}</Badge>}
              </div>

              <p className="text-muted-foreground mb-4">{service.description}</p>
            </CardContent>
            <CardFooter className="px-6 py-4 pt-0 flex gap-2">
              <Button asChild variant="outline" className="flex-1">
                <Link href={`/profile/services/${service.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="text-red-500 hover:text-red-600"
                onClick={() => openDeleteDialog(service)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Service</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the {serviceToDelete?.name} service? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => deleteService(serviceToDelete?.id)}>
              Delete Service
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
