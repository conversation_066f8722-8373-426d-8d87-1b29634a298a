import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin("./lib/i18n/server.ts");

/** @type {import('next').NextConfig} */
const nextConfig = {
	experimental: {
		devtoolSegmentExplorer: true,
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	images: {
		unoptimized: true,
	},
	async redirects() {
		return [
			// Handle non-localized legacy cat URLs
			{
				source: "/cats/:id(\\d+)",
				destination: "/api/redirect/cats/:id?locale=en",
				permanent: false,
			},
			{
				source: "/cats/:id(\\d+)/edit",
				destination: "/api/redirect/cats/:id?locale=en&edit=true",
				permanent: false,
			},
			// Handle localized legacy cat URLs
			{
				source: "/:locale(en|fr|ar)/cats/:id(\\d+)",
				destination: "/api/redirect/cats/:id?locale=:locale",
				permanent: false,
			},
			{
				source: "/:locale(en|fr|ar)/cats/:id(\\d+)/edit",
				destination: "/api/redirect/cats/:id?locale=:locale&edit=true",
				permanent: false,
			},
			// Handle non-localized legacy user URLs
			{
				source: "/users/:id(\\d+)",
				destination: "/api/redirect/users/:id?locale=en",
				permanent: false,
			},
			// Handle localized legacy user URLs
			{
				source: "/:locale(en|fr|ar)/users/:id(\\d+)",
				destination: "/api/redirect/users/:id?locale=:locale",
				permanent: false,
			},
		];
	},
};

export default withNextIntl(nextConfig);
