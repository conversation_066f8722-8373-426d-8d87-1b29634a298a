import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./schema";
import { drizzle as drizzleNode } from "drizzle-orm/node-postgres";
import { Pool } from "pg";

// Database connection string
const connectionString =
	process.env.DATABASE_URL ||
	"postgresql://postgres:postgres@localhost:5432/cat_adoption_db";

// Create postgres connection
const client = postgres(connectionString);

// Create drizzle database instance with prepared queries
const db = drizzle(client, {
	schema,
	logger: process.env.NODE_ENV === "development",
});

// Export schema for use in other files
export { schema, db };
