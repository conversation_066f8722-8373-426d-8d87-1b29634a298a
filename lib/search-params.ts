import {
	createSearchParamsCache,
	parseAsInteger,
	parseAsString,
	parseAsBoolean,
	parseAsStringLiteral,
} from "nuqs/server";

// Define individual parsers for reuse
export const searchParser = parseAsString.withDefault("");
export const pageParser = parseAsInteger.withDefault(1);
export const genderParser = parseAsStringLiteral(["all", "male", "female"]).withDefault("all");
export const ageMinParser = parseAsInteger.withDefault(0);
export const ageMaxParser = parseAsInteger.withDefault(20);
export const breedIdParser = parseAsString.withDefault("");
export const wilayaIdParser = parseAsString.withDefault("");
export const communeIdParser = parseAsString.withDefault("");
export const availableParser = parseAsBoolean.withDefault(false);
export const specialNeedsParser = parseAsBoolean.withDefault(false);
export const vaccinatedParser = parseAsBoolean.withDefault(false);
export const neuteredParser = parseAsBoolean.withDefault(false);
export const sortParser = parseAsStringLiteral([
	"newest",
	"oldest", 
	"name_asc",
	"name_desc"
]).withDefault("newest");

// Cat listing search parameters (used in cats page)
export const catListingParsers = {
	search: searchParser,
	page: pageParser,
	gender: genderParser,
	ageMin: ageMinParser,
	ageMax: ageMaxParser,
	breedId: breedIdParser,
	wilayaId: wilayaIdParser,
	communeId: communeIdParser,
	available: availableParser,
	specialNeeds: specialNeedsParser,
	vaccinated: vaccinatedParser,
	neutered: neuteredParser,
	sort: sortParser,
};

// Create server-side cache for cat listings
export const catListingCache = createSearchParamsCache(catListingParsers);

// Simple pagination parsers (used in profile pages)
export const paginationParsers = {
	page: pageParser,
};

// Create server-side cache for pagination
export const paginationCache = createSearchParamsCache(paginationParsers);

// Search-only parsers (for components that only need search)
export const searchOnlyParsers = {
	search: searchParser,
};

// Filter-only parsers (for filter components)
export const filterParsers = {
	gender: genderParser,
	ageMin: ageMinParser,
	ageMax: ageMaxParser,
	breedId: breedIdParser,
	wilayaId: wilayaIdParser,
	communeId: communeIdParser,
	available: availableParser,
	specialNeeds: specialNeedsParser,
	vaccinated: vaccinatedParser,
	neutered: neuteredParser,
	sort: sortParser,
};

// Export types for TypeScript support
export type CatListingSearchParams = {
	search: string;
	page: number;
	gender: "all" | "male" | "female";
	ageMin: number;
	ageMax: number;
	breedId: string;
	wilayaId: string;
	communeId: string;
	available: boolean;
	specialNeeds: boolean;
	vaccinated: boolean;
	neutered: boolean;
	sort: "newest" | "oldest" | "name_asc" | "name_desc";
};

export type PaginationSearchParams = {
	page: number;
};

export type SearchOnlyParams = {
	search: string;
};

export type FilterParams = {
	gender: "all" | "male" | "female";
	ageMin: number;
	ageMax: number;
	breedId: string;
	wilayaId: string;
	communeId: string;
	available: boolean;
	specialNeeds: boolean;
	vaccinated: boolean;
	neutered: boolean;
	sort: "newest" | "oldest" | "name_asc" | "name_desc";
};
