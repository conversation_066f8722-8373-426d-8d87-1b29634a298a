import { z } from "zod";
import {
	createTRPCRouter as router,
	publicProcedure,
	protectedProcedure,
} from "../trpc";
import {
	cats,
	catImages,
	favorites,
	chats,
	messages,
	chatParticipants,
} from "@/lib/db/schema";
import { eq, and, count, desc, inArray } from "drizzle-orm";
import { TRPCError } from "@trpc/server";
import { CatSummary, CatWithPagination, CatDetail } from "@/lib/types/cat";
import { generateUniqueCatSlug } from "@/lib/utils/slug";
import {
	formatCatSummary,
	getUserWithRole,
	fetchMissingRelatedData,
	analyzeJoinRequirements,
	buildCatFilters,
	buildSearchConditions,
	getSortOrder,
	getOptimizedCatQuery,
	getOptimizedCountQuery,
	transformCatResultsWithImages,
	logSlowQuery,
} from "./helpers/cat-helpers";

// Schema for cat creation and updates
const catInputSchema = z.object({
	name: z.string().min(2),
	gender: z.enum(["male", "female"]),
	age: z.number().min(0).max(25), // Changed to number with reasonable range
	breedId: z.string().optional(),
	description: z.string().min(20),
	story: z.string().optional(),
	wilayaId: z.string().optional(),
	communeId: z.string().optional(),
	vaccinated: z.boolean().default(false),
	neutered: z.boolean().default(false),
	specialNeeds: z.boolean().default(false),
	specialNeedsDescription: z.string().optional(),
	adopted: z.boolean().default(false),
	isDraft: z.boolean().default(true),
	images: z
		.array(
			z.object({
				url: z.string(),
				isPrimary: z.boolean().default(false),
			})
		)
		.min(1, "At least one image is required")
		.max(5, "Maximum of 5 images allowed")
		.transform((images) => images.slice(0, 5)), // Ensure only 5 images are processed
});

export const catsRouter = router({
	getFeatured: publicProcedure.query(
		async ({ ctx }): Promise<CatSummary[]> => {
			const startTime = performance.now();

			const featuredCats = await ctx.db.query.cats.findMany({
				where: eq(cats.featured, true),
				with: {
					breed: true,
					wilaya: true,
					commune: true,
				},
				limit: 3,
			});

			// Fetch only primary images for featured cats
			const catIds = featuredCats.map((cat) => cat.id);
			const primaryImages = await ctx.db.query.catImages.findMany({
				where: and(
					inArray(catImages.catId, catIds),
					eq(catImages.isPrimary, true)
				),
			});

			// Create a map of cat ID to primary image
			const primaryImageByCatId = primaryImages.reduce(
				(acc: any, img: any) => {
					acc[img.catId] = img;
					return acc;
				},
				{} as Record<number, any>
			);

			const duration = performance.now() - startTime;
			logSlowQuery("getFeatured", duration);

			// Add primary images to cats and format
			const catsWithImages = featuredCats.map((cat) => ({
				...cat,
				images: primaryImageByCatId[cat.id]
					? [primaryImageByCatId[cat.id]]
					: [],
			}));

			return catsWithImages.map(formatCatSummary);
		}
	),

	getAll: publicProcedure
		.input(
			z
				.object({
					gender: z.enum(["all", "male", "female"]).optional(),
					ageMin: z.number().optional(),
					ageMax: z.number().optional(),
					breedId: z.string().optional(),
					wilayaId: z.string().optional(),
					communeId: z.string().optional(),
					specialNeeds: z.boolean().optional(),
					vaccinated: z.boolean().optional(),
					neutered: z.boolean().optional(),
					notAdopted: z.boolean().optional(),
					search: z.string().min(1).optional(),
					page: z.number().default(1),
					limit: z.number().default(10),
					sort: z
						.enum(["newest", "oldest", "name_asc", "name_desc"])
						.optional(),
				})
				.optional()
		)
		.query(async ({ ctx, input }): Promise<CatWithPagination> => {
			const limit = input?.limit || 10;
			const offset = ((input?.page || 1) - 1) * limit;

			// Build base filter conditions (reusable for both queries and count)
			const baseConditions = buildCatFilters(input);
			const sortOrder = getSortOrder(input?.sort);

			// Analyze JOIN requirements based on input
			const hasSearch = !!(
				input?.search && input.search.trim().length > 0
			);
			const joinRequirements = analyzeJoinRequirements(input, hasSearch);

			// Execute optimized query based on requirements
			let allCats: any[];

			if (hasSearch) {
				// Search queries need all JOINs for searching across related tables
				const searchConditions = buildSearchConditions(input.search!);
				const searchResults = await getOptimizedCatQuery(
					ctx.db,
					baseConditions,
					sortOrder,
					limit,
					offset,
					{
						needsImages: true, // Include images in main query to eliminate N+1
						needsBreed: true,
						needsWilaya: true,
						needsCommune: true,
						needsUser: true,
					},
					searchConditions
				);

				// Transform results to include images in expected format
				allCats = transformCatResultsWithImages(searchResults, limit);
			} else {
				// Non-search queries: use conditional JOINs for optimal performance
				const optimizedResults = await getOptimizedCatQuery(
					ctx.db,
					baseConditions,
					sortOrder,
					limit,
					offset,
					joinRequirements
				);

				if (optimizedResults.length > 0) {
					// Fetch missing related data for display formatting
					const catsWithRelatedData = await fetchMissingRelatedData(
						ctx.db,
						optimizedResults,
						joinRequirements
					);

					// Transform results to include images in expected format
					allCats = transformCatResultsWithImages(
						catsWithRelatedData,
						limit
					);
				} else {
					allCats = [];
				}
			}

			// Get total count for pagination using optimized count query
			const totalCount = hasSearch
				? await getOptimizedCountQuery(
						ctx.db,
						baseConditions,
						joinRequirements,
						buildSearchConditions(input.search!)
					)
				: await getOptimizedCountQuery(
						ctx.db,
						baseConditions,
						joinRequirements
					);

			return {
				cats: allCats.map(formatCatSummary),
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / limit),
					page: input?.page || 1,
					limit,
				},
			};
		}),

	getById: publicProcedure
		.input(z.string())
		.query(async ({ ctx, input }): Promise<CatDetail | null> => {
			const catId = parseInt(input);
			if (isNaN(catId)) return null;

			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
				with: {
					images: true,
					user: true,
					breed: true,
					wilaya: true,
					commune: true,
				},
			});

			if (!cat) return null;

			if (
				cat.isDraft &&
				ctx.user &&
				cat.userId !== parseInt(ctx.user.id)
			) {
				return null;
			}

			// Get the basic cat summary first
			const catSummary = formatCatSummary(cat);

			// Then add the additional detail fields
			return {
				...catSummary,
				description: cat.description,
				story: cat.story,
				specialNeedsDescription: cat.specialNeedsDescription,
				breedName: cat.breed?.name,
				images: cat.images,
				userId: cat.userId,
				user: cat.user,
			};
		}),

	getBySlug: publicProcedure
		.input(z.string())
		.query(async ({ ctx, input }): Promise<CatDetail | null> => {
			// URL decode the slug to handle Arabic and other special characters
			const decodedSlug = decodeURIComponent(input);

			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.slug, decodedSlug),
				with: {
					images: true,
					user: true,
					breed: true,
					wilaya: true,
					commune: true,
				},
			});

			if (!cat) return null;

			if (
				cat.isDraft &&
				ctx.user &&
				cat.userId !== parseInt(ctx.user.id)
			) {
				return null;
			}

			// Get the basic cat summary first
			const catSummary = formatCatSummary(cat);

			// Then add the additional detail fields
			return {
				...catSummary,
				description: cat.description,
				story: cat.story,
				specialNeedsDescription: cat.specialNeedsDescription,
				breedName: cat.breed?.name,
				images: cat.images,
				userId: cat.userId,
				user: cat.user,
			};
		}),

	// Get cats by user (for profile page)
	getUserCats: protectedProcedure
		.input(
			z.object({
				userId: z.string().optional(),
				includeDrafts: z.boolean().default(false),
				page: z.number().default(1),
				limit: z.number().default(12),
				getAllCats: z.boolean().default(false), // Flag to get all cats without pagination
			})
		)
		.query(async ({ ctx, input }): Promise<CatWithPagination> => {
			// If no userId is provided, use the current user's ID
			const userId = input.userId
				? parseInt(input.userId)
				: parseInt(ctx.user.id);

			// Check if the user is requesting their own cats or if they're an admin
			const isOwnProfile = userId === parseInt(ctx.user.id);
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			const isAdmin = currentUser?.role === "admin";

			// If requesting someone else's drafts and not an admin, throw error
			if (input.includeDrafts && !isOwnProfile && !isAdmin) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You can only view your own drafts",
				});
			}

			// Build query conditions
			let conditions = [eq(cats.userId, userId)];

			// Only include published cats unless specifically requesting drafts
			if (!input.includeDrafts) {
				conditions.push(eq(cats.isDraft, false));
			}

			// Calculate pagination (only if not getting all cats)
			const limit = input.getAllCats ? undefined : input.limit;
			const offset = input.getAllCats
				? undefined
				: (input.page - 1) * input.limit;

			// Get cats with pagination (or all if getAllCats is true)
			const userCats = await ctx.db.query.cats.findMany({
				where: and(...conditions),
				with: {
					images: true,
					breed: true,
					wilaya: true,
					commune: true,
				},
				orderBy: desc(cats.createdAt),
				limit,
				offset,
			});

			// Get total count for pagination (even if getting all cats, for consistency)
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(cats)
				.where(conditions.length > 0 ? and(...conditions) : undefined);

			return {
				cats: userCats.map(formatCatSummary),
				pagination: {
					total: totalCount,
					pageCount: input.getAllCats
						? 1
						: Math.ceil(totalCount / input.limit),
					page: input.getAllCats ? 1 : input.page,
					limit: input.getAllCats ? totalCount : input.limit,
				},
			};
		}),

	// Create a new cat
	create: protectedProcedure
		.input(catInputSchema)
		.mutation(async ({ ctx, input }) => {
			// Check if user is authorized to create cats (rescuer or clinic)
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			if (
				currentUser?.role !== "rescuer" &&
				currentUser?.role !== "clinic"
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only rescuers and clinics can create cat listings",
				});
			}

			// Generate a unique slug for the cat
			const slug = await generateUniqueCatSlug(input.name);

			// Process the input data
			const catData = {
				name: input.name,
				slug: slug,
				gender: input.gender,
				age: input.age,
				breedId: input.breedId ? parseInt(input.breedId) : null,
				description: input.description,
				story: input.story || null,
				wilayaId: input.wilayaId ? parseInt(input.wilayaId) : null,
				communeId: input.communeId ? parseInt(input.communeId) : null,
				vaccinated: input.vaccinated,
				neutered: input.neutered,
				specialNeeds: input.specialNeeds,
				specialNeedsDescription: input.specialNeeds
					? input.specialNeedsDescription
					: null,
				adopted: input.adopted,
				isDraft: input.isDraft,
				userId: parseInt(ctx.user.id),
			};

			// Insert the cat into the database
			const [newCat] = await ctx.db
				.insert(cats)
				.values(catData)
				.returning();

			if (!newCat) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create cat listing",
				});
			}

			// Insert cat images - limit to maximum 5 images
			const imageInserts = input.images.slice(0, 5).map((image) => ({
				catId: newCat.id,
				url: image.url,
				isPrimary: image.isPrimary,
			}));

			await ctx.db.insert(catImages).values(imageInserts);

			return {
				id: newCat.id,
				message: input.isDraft
					? "Cat saved as draft"
					: "Cat published successfully",
			};
		}),

	// Update an existing cat
	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				data: catInputSchema,
			})
		)
		.mutation(async ({ ctx, input }) => {
			const catId = parseInt(input.id);
			if (isNaN(catId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid cat ID",
				});
			}

			// Get the cat to check ownership
			const existingCat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!existingCat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is authorized to update this cat
			const isOwner = existingCat.userId === parseInt(ctx.user.id);
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			const isAdmin = currentUser?.role === "admin";

			if (!isOwner && !isAdmin) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You don't have permission to update this cat",
				});
			}

			// Generate a new slug if the name has changed
			let slug = existingCat.slug;
			if (input.data.name !== existingCat.name) {
				slug = await generateUniqueCatSlug(input.data.name, catId);
			}

			// Process the input data
			const catData = {
				name: input.data.name,
				slug: slug,
				gender: input.data.gender,
				age: input.data.age,
				breedId: input.data.breedId
					? parseInt(input.data.breedId)
					: null,
				description: input.data.description,
				story: input.data.story || null,
				wilayaId: input.data.wilayaId
					? parseInt(input.data.wilayaId)
					: null,
				communeId: input.data.communeId
					? parseInt(input.data.communeId)
					: null,
				vaccinated: input.data.vaccinated,
				neutered: input.data.neutered,
				specialNeeds: input.data.specialNeeds,
				specialNeedsDescription: input.data.specialNeeds
					? input.data.specialNeedsDescription
					: null,
				adopted: input.data.adopted,
				isDraft: input.data.isDraft,
				updatedAt: new Date(),
			};

			// Update the cat
			await ctx.db.update(cats).set(catData).where(eq(cats.id, catId));

			// Handle image updates - first delete existing images
			await ctx.db.delete(catImages).where(eq(catImages.catId, catId));

			// Then insert new images - limit to maximum 5 images
			const imageInserts = input.data.images.slice(0, 5).map((image) => ({
				catId: catId,
				url: image.url,
				isPrimary: image.isPrimary,
			}));

			await ctx.db.insert(catImages).values(imageInserts);

			return {
				id: catId,
				message: input.data.isDraft
					? "Cat saved as draft"
					: "Cat updated successfully",
			};
		}),

	// Delete a cat
	delete: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			const catId = parseInt(input);
			if (isNaN(catId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid cat ID",
				});
			}

			// Get the cat to check ownership
			const existingCat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!existingCat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is authorized to delete this cat
			const isOwner = existingCat.userId === parseInt(ctx.user.id);
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			const isAdmin = currentUser?.role === "admin";

			if (!isOwner && !isAdmin) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You don't have permission to delete this cat",
				});
			}

			try {
				// Delete all related data in the correct order to avoid foreign key constraint violations

				// 1. First, get all chats related to this cat
				const relatedChats = await ctx.db.query.chats.findMany({
					where: eq(chats.catId, catId),
				});

				// 2. Delete messages for all related chats
				for (const chat of relatedChats) {
					await ctx.db
						.delete(messages)
						.where(eq(messages.chatId, chat.id));
				}

				// 3. Delete chat participants for all related chats
				for (const chat of relatedChats) {
					await ctx.db
						.delete(chatParticipants)
						.where(eq(chatParticipants.chatId, chat.id));
				}

				// 4. Delete all chats related to this cat
				await ctx.db.delete(chats).where(eq(chats.catId, catId));

				// 5. Delete all favorites for this cat
				await ctx.db
					.delete(favorites)
					.where(eq(favorites.catId, catId));

				// 6. Delete all related images
				await ctx.db
					.delete(catImages)
					.where(eq(catImages.catId, catId));

				// 7. Finally, delete the cat
				await ctx.db.delete(cats).where(eq(cats.id, catId));
			} catch (error) {
				console.error("Error deleting cat:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to delete cat",
				});
			}

			return {
				id: catId,
				message: "Cat deleted successfully",
			};
		}),

	// Toggle draft status
	toggleDraftStatus: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				isDraft: z.boolean(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const catId = parseInt(input.id);
			if (isNaN(catId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid cat ID",
				});
			}

			// Get the cat to check ownership
			const existingCat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!existingCat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is authorized to update this cat
			const isOwner = existingCat.userId === parseInt(ctx.user.id);
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			const isAdmin = currentUser?.role === "admin";

			if (!isOwner && !isAdmin) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You don't have permission to update this cat",
				});
			}

			// Update the draft status
			await ctx.db
				.update(cats)
				.set({
					isDraft: input.isDraft,
					updatedAt: new Date(),
				})
				.where(eq(cats.id, catId));

			return {
				id: catId,
				message: input.isDraft
					? "Cat saved as draft"
					: "Cat published successfully",
			};
		}),

	// Update cat status
	updateStatus: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				status: z.enum([
					"available",
					"pending",
					"adopted",
					"unavailable",
				]),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const catId = parseInt(input.id);
			if (isNaN(catId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid cat ID",
				});
			}

			// Get the cat to check ownership
			const existingCat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!existingCat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is authorized to update this cat
			const isOwner = existingCat.userId === parseInt(ctx.user.id);
			const currentUser = await getUserWithRole(ctx, ctx.user.id);
			const isAdmin = currentUser?.role === "admin";

			if (!isOwner && !isAdmin) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You don't have permission to update this cat",
				});
			}

			// Update the status and adopted fields for backward compatibility
			const isAdopted = input.status === "adopted";

			await ctx.db
				.update(cats)
				.set({
					status: input.status,
					adopted: isAdopted,
					updatedAt: new Date(),
				})
				.where(eq(cats.id, catId));

			return {
				id: catId.toString(),
				status: input.status,
				message: `Status updated to ${input.status}`,
			};
		}),
});
