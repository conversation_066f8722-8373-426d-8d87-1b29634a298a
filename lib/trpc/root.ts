import { createCallerFactory, createTR<PERSON><PERSON>outer } from "./trpc";
import { catsRouter } from "./routers/cats";
import { statsRouter } from "./routers/stats";
import { usersRouter } from "./routers/users";
import { clinicsRouter } from "./routers/clinics";
import { breedsRouter } from "./routers/breeds";
import { locationRouter } from "./routers/location";
import { messagesRouter } from "./routers/messages";
import { favoritesRouter } from "./routers/favorites";

// Create the app router
export const appRouter = createTRPCRouter({
	cats: catsRouter,
	stats: statsRouter,
	users: usersRouter,
	clinics: clinicsRouter,
	breeds: breedsRouter,
	location: locationRouter,
	messages: messagesRouter,
	favorites: favoritesRouter,
});

// Export type router type signature,
// NOT the router itself.
export type AppRouter = typeof appRouter;
export const createCaller = createCallerFactory(appRouter);
