import { userRoleEnum } from "@/lib/db/schema";

export interface User {
	id: number;
	name: string;
	email: string;
	role: (typeof userRoleEnum.enumValues)[number];
	bio: string | null;
	location: string | null;
	wilayaId: number | null;
	communeId: number | null;
	phone: string | null;
	image: string | null;
	createdAt: string | Date;
	updatedAt: string | Date;
	emailVerified: boolean;
	wilaya?: {
		id: number;
		name: string;
		code: string;
	} | null;
	commune?: {
		id: number;
		name: string;
	} | null;
}

export interface SidebarItem {
	id: string;
	labelKey: string;
	icon: React.ComponentType<{ className?: string }>;
	descriptionKey: string;
}

export interface QuickStat {
	value: number;
	labelKey: string;
	color: "teal" | "purple" | "green" | "blue" | "orange";
}

export interface DashboardStats {
	[key: string]: number;
}
