import { db } from "@/lib/db";
import { cats, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

/**
 * Generate a URL-friendly slug from a string
 * Supports Arabic characters (U+0600-U+06FF) alongside Latin characters
 * @param text - The text to convert to a slug
 * @returns A URL-friendly slug
 */
export function generateSlug(text: string): string {
	const baseSlug = text
		.toLowerCase()
		.trim()
		// Replace spaces and underscores with hyphens, preserve Arabic and Latin characters
		// Arabic range: U+0600-U+06FF, Latin: a-z, A-Z, 0-9
		.replace(/[^\u0600-\u06FFa-zA-Z0-9\s_-]/g, "")
		.replace(/[\s_]+/g, "-")
		// Replace multiple consecutive hyphens with single hyphen
		.replace(/-+/g, "-")
		// Remove leading/trailing hyphens
		.replace(/^-+|-+$/g, "");

	// If the slug is purely numeric, prefix it to avoid ID/slug confusion
	if (/^\d+$/.test(baseSlug)) {
		return `cat-${baseSlug}`;
	}

	return baseSlug;
}

/**
 * Generate a unique slug for a cat
 * @param name - The cat's name
 * @param excludeId - Optional cat ID to exclude from uniqueness check (for updates)
 * @returns A unique slug for the cat
 */
export async function generateUniqueCatSlug(
	name: string,
	excludeId?: number
): Promise<string> {
	const baseSlug = generateSlug(name);
	let slug = baseSlug;
	let counter = 1;

	while (true) {
		// Check if slug exists
		const existingCat = await db.query.cats.findFirst({
			where: eq(cats.slug, slug),
		});

		// If no existing cat found, or it's the same cat we're updating, use this slug
		if (!existingCat || (excludeId && existingCat.id === excludeId)) {
			return slug;
		}

		// Generate a new slug with counter
		slug = `${baseSlug}-${counter}`;
		counter++;

		// Safety check to prevent infinite loops
		if (counter > 1000) {
			// Add timestamp as fallback
			slug = `${baseSlug}-${Date.now()}`;
			break;
		}
	}

	return slug;
}

/**
 * Generate a unique slug for a user
 * @param name - The user's name
 * @param excludeId - Optional user ID to exclude from uniqueness check (for updates)
 * @returns A unique slug for the user
 */
export async function generateUniqueUserSlug(
	name: string,
	excludeId?: number
): Promise<string> {
	const baseSlug = generateSlug(name);
	let slug = baseSlug;
	let counter = 1;

	while (true) {
		// Check if slug exists
		const existingUser = await db.query.users.findFirst({
			where: eq(users.slug, slug),
		});

		// If no existing user found, or it's the same user we're updating, use this slug
		if (!existingUser || (excludeId && existingUser.id === excludeId)) {
			return slug;
		}

		// Generate a new slug with counter
		slug = `${baseSlug}-${counter}`;
		counter++;

		// Safety check to prevent infinite loops
		if (counter > 1000) {
			// Add timestamp as fallback
			slug = `${baseSlug}-${Date.now()}`;
			break;
		}
	}

	return slug;
}

/**
 * Validate a slug format
 * Supports Arabic characters alongside Latin characters
 * @param slug - The slug to validate
 * @returns True if the slug is valid
 */
export function isValidSlug(slug: string): boolean {
	// Slug should only contain Arabic characters (U+0600-U+06FF), Latin letters, numbers, and hyphens
	// Should not start or end with hyphens
	// Should be between 1 and 100 characters
	// Purely numeric slugs are invalid (they should be prefixed)
	const slugRegex = /^[\u0600-\u06FFa-z0-9]+(?:-[\u0600-\u06FFa-z0-9]+)*$/;
	const isValidFormat =
		slugRegex.test(slug) && slug.length >= 1 && slug.length <= 100;

	// Additional check: purely numeric slugs are invalid (should be prefixed like "cat-4444")
	const isPurelyNumeric = /^\d+$/.test(slug);

	return isValidFormat && !isPurelyNumeric;
}

/**
 * Check if a cat slug is available
 * @param slug - The slug to check
 * @param excludeId - Optional cat ID to exclude from the check
 * @returns True if the slug is available
 */
export async function isCatSlugAvailable(
	slug: string,
	excludeId?: number
): Promise<boolean> {
	if (!isValidSlug(slug)) {
		return false;
	}

	const existingCat = await db.query.cats.findFirst({
		where: eq(cats.slug, slug),
	});

	return (
		!existingCat ||
		(excludeId !== undefined && existingCat.id === excludeId)
	);
}

/**
 * Check if a user slug is available
 * @param slug - The slug to check
 * @param excludeId - Optional user ID to exclude from the check
 * @returns True if the slug is available
 */
export async function isUserSlugAvailable(
	slug: string,
	excludeId?: number
): Promise<boolean> {
	if (!isValidSlug(slug)) {
		return false;
	}

	const existingUser = await db.query.users.findFirst({
		where: eq(users.slug, slug),
	});

	return (
		!existingUser ||
		(excludeId !== undefined && existingUser.id === excludeId)
	);
}
