import { redirect } from "next/navigation";
import { AdminDashboard } from "@/components/admin/dashboard";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "admin" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title") || "Admin Dashboard"} - ${common("appName")}`,
		description: t("description") || "Manage users, cats, and site content",
	};
}

// This would normally check the user's session server-side
async function getUser() {
	// Simulate API call
	await new Promise((resolve) => setTimeout(resolve, 100));

	// Mock user for demo
	return {
		id: "admin1",
		name: "Admin User",
		role: "admin",
	};
}

export default async function AdminPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations("admin");

	const user = await getUser();

	// Redirect if not admin
	if (user?.role !== "admin") {
		redirect(`/${lang}/auth/login`);
	}

	return (
		<main className="container py-10">
			<h1 className="text-3xl font-bold mb-6">
				{t("title") || "Admin Dashboard"}
			</h1>
			<AdminDashboard />
		</main>
	);
}
