import { redirect } from "@/lib/i18n/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { ClinicServices } from "@/components/profile/clinic-services";

interface ProfileServicesPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile",
	});

	return {
		title: `${t("clinicServices")} - ${t("title")}`,
		description: t("servicesDescription"),
	};
}

export default async function ProfileServicesPage({
	params,
}: ProfileServicesPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();
	const t = await getTranslations("profile");

	// Check if user has permission to access this page
	if (user.role !== "clinic") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					{t("clinicServices")}
				</h1>
				<p className="text-gray-600 mt-1">{t("servicesDescription")}</p>
			</div>
			<ClinicServices />
		</div>
	);
}
