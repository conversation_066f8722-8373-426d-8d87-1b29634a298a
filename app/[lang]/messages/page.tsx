import { Card, CardContent } from "@/components/ui/card";
import { MessageSquare } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({
		locale: lang,
		namespace: "profile.messages",
	});
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title") || "Messages"} - ${common("appName")}`,
		description: t("description") || "Your conversations",
	};
}

export default async function MessagesPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations("profile.messages");

	return (
		<div className="flex h-full flex-col items-center justify-center">
			<Card className="w-full max-w-md">
				<CardContent className="flex flex-col items-center justify-center py-12">
					<div className="mb-4 rounded-full bg-muted p-3">
						<MessageSquare className="h-6 w-6 text-muted-foreground" />
					</div>
					<h2 className="mb-2 text-xl font-medium">
						{t("selectConversation") || "Select a conversation"}
					</h2>
					<p className="text-center text-muted-foreground">
						{t("chooseToStart") ||
							"Choose a conversation from the sidebar to start chatting"}
					</p>
				</CardContent>
			</Card>
		</div>
	);
}
